# Dependencies
node_modules/
/.pnp
.pnp.js

# Build outputs
/frontend/build/
/backend/dist/

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Testing
/coverage
/backend/coverage

# Temporary files
*.log
*.tmp

# System Files
.DS_Store
Thumbs.db

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-

postman/
__pycache__/

# Added by <PERSON> Task Master
# Logs
logs
dev-debug.log
# Dependency directories
# Environment variables
.env
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/ 

.cursor/
.roo/
scripts/
.env.example
.roomodes
.taskmasterconfig
.windsurfrules
.taskmaster/